#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <errno.h>
#include <sys/stat.h>

#define TEST_FILE "test_data.txt"
#define BUFFER_SIZE 256

// 创建测试文件的函数
int create_test_file() {
    int fd;
    const char *test_content = "This is a test file\n"
                              "The second line is used to test the lseek and read functions。\n"
                              "The third line:ABCDEFGHIJKLMNOPQRSTUVWXYZ\n"
                              "The fouth line:0123456789\n"
                              "The last line:End of file flag\n";
    
    printf("正在创建测试文件: %s\n", TEST_FILE);
    
    fd = open(TEST_FILE, O_CREAT | O_WRONLY | O_TRUNC, 0644);
    if (fd == -1) {
        perror("创建测试文件失败");
        return -1;
    }
    
    if (write(fd, test_content, strlen(test_content)) == -1) {
        perror("写入测试文件失败");
        close(fd);
        return -1;
    }
    
    close(fd);
    printf("测试文件创建成功，内容长度: %zu 字节\n\n", strlen(test_content));
    return 0;
}

// 显示文件内容的函数
void display_file_content() {
    int fd;
    char buffer[BUFFER_SIZE];
    ssize_t bytes_read;
    
    printf("=== 完整文件内容 ===\n");
    fd = open(TEST_FILE, O_RDONLY);
    if (fd == -1) {
        perror("打开文件失败");
        return;
    }
    
    while ((bytes_read = read(fd, buffer, sizeof(buffer) - 1)) > 0) {
        buffer[bytes_read] = '\0';
        printf("%s", buffer);
    }
    
    close(fd);
    printf("==================\n\n");
}

// 测试SEEK_SET（从文件开头定位）
void test_seek_set() {
    int fd;
    char buffer[50];
    off_t offset;
    ssize_t bytes_read;
    
    printf("=== 测试 SEEK_SET（从文件开头定位）===\n");
    
    fd = open(TEST_FILE, O_RDONLY);
    if (fd == -1) {
        perror("打开文件失败");
        return;
    }
    
    // 定位到文件开头第10个字节
    offset = k3_lseek(fd, 10, SEEK_SET);
    if (offset == -1) {
        perror("lseek SEEK_SET 失败");
        close(fd);
        return;
    }
    
    printf("使用 lseek(fd, 10, SEEK_SET) 定位到位置: %ld\n", (long)offset);
    
    // 读取20个字节
    bytes_read = read(fd, buffer, 20);
    if (bytes_read == -1) {
        perror("read 失败");
        close(fd);
        return;
    }
    
    buffer[bytes_read] = '\0';
    printf("从位置10开始读取20字节: \"%s\"\n", buffer);
    
    close(fd);
    printf("\n");
}

// 测试SEEK_CUR（从当前位置定位）
void test_seek_cur() {
    int fd;
    char buffer[30];
    off_t offset;
    ssize_t bytes_read;
    
    printf("=== 测试 SEEK_CUR（从当前位置定位）===\n");
    
    fd = open(TEST_FILE, O_RDONLY);
    if (fd == -1) {
        perror("打开文件失败");
        return;
    }
    
    // 先读取15个字节
    bytes_read = read(fd, buffer, 15);
    if (bytes_read == -1) {
        perror("初始read失败");
        close(fd);
        return;
    }
    buffer[bytes_read] = '\0';
    printf("初始读取15字节: \"%s\"\n", buffer);
    
    // 获取当前位置
    offset = k3_lseek(fd, 0, SEEK_CUR);
    printf("当前文件指针位置: %ld\n", (long)offset);
    
    // 从当前位置向前移动5个字节
    offset = k3_lseek(fd, 5, SEEK_CUR);
    if (offset == -1) {
        perror("lseek SEEK_CUR 失败");
        close(fd);
        return;
    }
    
    printf("使用 lseek(fd, 5, SEEK_CUR) 移动后位置: %ld\n", (long)offset);
    
    // 读取10个字节
    bytes_read = read(fd, buffer, 10);
    if (bytes_read == -1) {
        perror("read 失败");
        close(fd);
        return;
    }
    
    buffer[bytes_read] = '\0';
    printf("从新位置读取10字节: \"%s\"\n", buffer);
    
    close(fd);
    printf("\n");
}

// 测试SEEK_END（从文件末尾定位）
void test_seek_end() {
    int fd;
    char buffer[50];
    off_t offset;
    ssize_t bytes_read;
    struct stat file_stat;
    
    printf("=== 测试 SEEK_END（从文件末尾定位）===\n");
    
    fd = open(TEST_FILE, O_RDONLY);
    if (fd == -1) {
        perror("打开文件失败");
        return;
    }
    
    // 获取文件大小
    if (fstat(fd, &file_stat) == -1) {
        perror("获取文件信息失败");
        close(fd);
        return;
    }
    printf("文件总大小: %ld 字节\n", (long)file_stat.st_size);
    
    // 定位到文件末尾
    offset = k3_lseek(fd, 0, SEEK_END);
    if (offset == -1) {
        perror("lseek SEEK_END 失败");
        close(fd);
        return;
    }
    printf("使用 lseek(fd, 0, SEEK_END) 定位到文件末尾，位置: %ld\n", (long)offset);
    
    // 从文件末尾向前30个字节
    offset = k3_lseek(fd, -30, SEEK_END);
    if (offset == -1) {
        perror("lseek SEEK_END -30 失败");
        close(fd);
        return;
    }
    printf("使用 lseek(fd, -30, SEEK_END) 从末尾向前30字节，位置: %ld\n", (long)offset);
    
    // 读取到文件末尾
    bytes_read = read(fd, buffer, sizeof(buffer) - 1);
    if (bytes_read == -1) {
        perror("read 失败");
        close(fd);
        return;
    }
    
    buffer[bytes_read] = '\0';
    printf("从该位置读取到文件末尾的内容: \"%s\"\n", buffer);
    
    close(fd);
    printf("\n");
}

// 测试错误情况
void test_error_cases() {
    int fd;
    off_t offset;
    
    printf("=== 测试错误情况 ===\n");
    
    fd = open(TEST_FILE, O_RDONLY);
    if (fd == -1) {
        perror("打开文件失败");
        return;
    }
    
    // 尝试定位到负数位置（应该失败）
    printf("尝试定位到负数位置...\n");
    offset = k3_lseek(fd, -10, SEEK_SET);
    if (offset == -1) {
        printf("预期的错误: %s\n", strerror(errno));
    } else {
        printf("意外成功，位置: %ld\n", (long)offset);
    }
    
    close(fd);
    
    // 尝试对无效文件描述符进行lseek
    printf("尝试对无效文件描述符进行lseek...\n");
    offset = k3_lseek(-1, 0, SEEK_SET);
    if (offset == -1) {
        printf("预期的错误: %s\n", strerror(errno));
    }
    
    printf("\n");
}

int main() {
    printf("=== lseek() 和 read() 系统调用测试程序 ===\n\n");
    
    // 创建测试文件
    if (create_test_file() != 0) {
        return 1;
    }
    
    // 显示文件内容
    display_file_content();
    
    // 执行各种测试
    test_seek_set();
    test_seek_cur();
    test_seek_end();
    test_error_cases();
    
    printf("=== 测试完成 ===\n");
    printf("注意：测试文件 %s 已创建，您可以手动查看或删除它。\n", TEST_FILE);
    
    return 0;
}
