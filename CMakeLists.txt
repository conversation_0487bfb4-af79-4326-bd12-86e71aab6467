帮我编译一下Cmake规则，将k3_lseek.c编译成静态库，而且带fPic标志,同时还需要带上这些
# 设置 C 和 C++ 的标准
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 链接器优化
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--gc-sections")

# 启用LTO
set(CMAKE_INTERPROCEDURAL_OPTIMIZATION ON)


message(STATUS "Loading arm toolchain from arm-toolchain.cmake")

# 设置目标系统名称
SET(CMAKE_SYSTEM_NAME Linux)

# 设置目标处理器架构
SET(CMAKE_SYSTEM_PROCESSOR arm)

# 指定交叉编译器的路径
SET(CMAKE_C_COMPILER /home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-gcc)
SET(CMAKE_CXX_COMPILER /home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-g++)

# 指定 strip 工具路径
SET(CMAKE_STRIP /home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-strip)

